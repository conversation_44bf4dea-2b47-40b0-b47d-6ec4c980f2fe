import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { reference } = body;

    if (!reference) {
      return NextResponse.json(
        { success: false, message: 'Payment reference is required' },
        { status: 400 }
      );
    }

    const NEXT_PUBLIC_PAYSTACK_SECRET_KEY = process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY;
    
    if (!NEXT_PUBLIC_PAYSTACK_SECRET_KEY || NEXT_PUBLIC_PAYSTACK_SECRET_KEY === 'sk_test_your_NEXT_PUBLIC_PAYSTACK_SECRET_KEY') {
      console.error('Paystack secret key not configured properly. Available env vars:', Object.keys(process.env).filter(key => key.includes('PAYSTACK')));
      return NextResponse.json(
        { 
          success: false, 
          message: 'Payment system not configured properly. Please check environment variables.',
          debug: process.env.NODE_ENV === 'development' ? {
            hasKey: !!NEXT_PUBLIC_PAYSTACK_SECRET_KEY,
            availableKeys: Object.keys(process.env).filter(key => key.includes('PAYSTACK'))
          } : undefined
        },
        { status: 500 }
      );
    }

    const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      headers: {
        'Authorization': `Bearer ${NEXT_PUBLIC_PAYSTACK_SECRET_KEY}`,
      }
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.error('Paystack verification error:', result);
      return NextResponse.json(
        { success: false, message: result.message || 'Failed to verify payment' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Error verifying payment:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
} 