"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Settings } from "lucide-react";
import NotificationDropdown from "./NotificationDropdown";
import { Button } from "@/components/ui/button";
import { getCurrentUser, getUserProfile } from "@/lib/auth-supabase";

export default function Header() {
  const [displayName, setDisplayName] = useState<string>("User");
  const [email, setEmail] = useState<string>("");
  const [avatar, setAvatar] = useState<string>("https://randomuser.me/api/portraits/men/32.jpg");
  const [userType, setUserType] = useState<'individual' | 'corporate' | null>(null);

  // Determine base path based on user type
  const basePath = userType === 'corporate' ? '/dashboard/lender' : '/dashboard/borrower';

  useEffect(() => {
    async function fetchUser() {
      const user = await getCurrentUser();
      if (user) {
        setEmail(user.email || "");
        const profile = await getUserProfile(user.id);
        if (profile) {
          setUserType(profile.user_type);
          if (profile.user_type === "individual") {
            setDisplayName(profile.full_name || user.email || "User");
            if (profile.avatar_url) setAvatar(profile.avatar_url);
          } else if (profile.user_type === "corporate") {
            setDisplayName(profile.organization_name || user.email || "User");
            if (profile.logo_url) setAvatar(profile.logo_url);
          }
        } else {
          setDisplayName(user.email || "User");
        }
      }
    }
    fetchUser();
  }, []);

  return (
    <header className="bg-white border-b">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <Image
            src={avatar}
            alt="User avatar"
            width={40}
            height={40}
            className="rounded-full"
          />
          <div>
            <h2 className="text-lg font-semibold">{displayName}</h2>
            <p className="text-sm text-gray-500">{email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <NotificationDropdown basePath={basePath} />
          <Link href={`${basePath}/settings`}>
            <Button
              variant="ghost"
              size="icon"
              className="p-2 hover:bg-gray-100 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
