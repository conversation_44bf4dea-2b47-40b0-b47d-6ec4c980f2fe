# Payment System Setup Guide

This guide explains how to configure the payment system with Paystack integration.

## Required Environment Variables

Add these to your `.env.local` file:

```env
# Application URL (required for callbacks)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Paystack Configuration
NEXT_PUBLIC_PAYSTACK_SECRET_KEY=sk_test_your_actual_NEXT_PUBLIC_PAYSTACK_SECRET_KEY
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_actual_paystack_public_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
DATABASE_URL=your_database_connection_string
```

## Callback URLs

### 1. Payment Success Callback
After successful payment, users are redirected to:
```
https://yourdomain.com/payment/success?reference=PAYMENT_REFERENCE
```

### 2. Webhook URL for Paystack
Configure this webhook URL in your Paystack dashboard:
```
https://yourdomain.com/api/payments/webhook
```

## Paystack Dashboard Configuration

1. **Login to Paystack Dashboard**
   - Go to [https://dashboard.paystack.co](https://dashboard.paystack.co)
   - Login with your Paystack account

2. **Configure Webhook URL**
   - Navigate to Settings → Webhooks
   - Add webhook URL: `https://yourdomain.com/api/payments/webhook`
   - Select events: `charge.success`, `charge.failed`, `charge.disputed`

3. **Get API Keys**
   - Navigate to Settings → API Keys & Webhooks
   - Copy your **Public Key** (starts with `pk_test_` or `pk_live_`)
   - Copy your **Secret Key** (starts with `sk_test_` or `sk_live_`)

## Payment Flow

1. **Payment Initiation**
   - User clicks "Make Payment" in the application
   - PaymentModal opens with payment options
   - User selects amount and clicks "Pay Now"

2. **Paystack Redirect**
   - User is redirected to Paystack payment page
   - User completes payment with their preferred method

3. **Callback Handling**
   - Paystack redirects user back to `/payment/success?reference=PAYMENT_REFERENCE`
   - Success page verifies payment and shows confirmation

4. **Webhook Processing**
   - Paystack sends webhook notification to `/api/payments/webhook`
   - System updates payment status in database
   - Payment schedule is updated if applicable

## URLs Summary

| Purpose | URL | Method |
|---------|-----|---------|
| Payment Success | `/payment/success?reference=REF` | GET |
| Payment Failure | `/payment/success?reference=REF` (with error) | GET |
| Initialize Payment | `/api/payments/initialize` | POST |
| Verify Payment | `/api/payments/verify` | POST |
| Webhook Handler | `/api/payments/webhook` | POST |

## Development vs Production

### Development
- Use `NEXT_PUBLIC_APP_URL=http://localhost:3000`
- Use Paystack test keys (`pk_test_` and `sk_test_`)
- Webhook URL: `http://localhost:3000/api/payments/webhook` (use ngrok for testing)

### Production
- Use your actual domain: `NEXT_PUBLIC_APP_URL=https://yourdomain.com`
- Use Paystack live keys (`pk_live_` and `sk_live_`)
- Webhook URL: `https://yourdomain.com/api/payments/webhook`

## Security Features

- ✅ Webhook signature verification
- ✅ Server-side payment verification
- ✅ Secret keys handled server-side only
- ✅ Payment reference validation
- ✅ Database transaction logging

## Testing

1. **Test Payment Flow**
   - Use Paystack test card: `****************`
   - CVV: `408`
   - Expiry: Any future date
   - PIN: `0000`

2. **Test Webhook**
   - Use ngrok to expose local webhook endpoint
   - Monitor webhook events in Paystack dashboard
   - Check application logs for webhook processing

## Troubleshooting

### Common Issues

1. **Invalid Key Error**
   - Ensure environment variables are set correctly
   - Check if using correct test/live keys
   - Verify `.env.local` file is in project root

2. **Webhook Not Received**
   - Check webhook URL in Paystack dashboard
   - Ensure webhook endpoint is accessible
   - Verify webhook signature validation

3. **Payment Not Updating**
   - Check webhook processing logs
   - Verify database connection
   - Ensure payment reference matches

### Debug Steps

1. Check browser console for client-side errors
2. Check server logs for API errors
3. Check Paystack dashboard for webhook delivery status
4. Verify database records are being created/updated

## Support

For issues with:
- **Paystack integration**: Check Paystack documentation
- **Database issues**: Check Supabase logs
- **Application errors**: Check Next.js logs 