"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, CheckCircle, Clock, AlertCircle, FileText, Save, Plus, User, Building2 } from "lucide-react";
import { 
  getCurrentUser,
  getUserProfile,
  updateUserProfile,
  uploadDocument,
  getUserDocuments
} from "@/lib/auth-supabase";
import { Database } from "@/lib/supabase";

// Document status type
type DocumentStatus = 'pending' | 'uploaded' | 'verified' | 'rejected';

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [userType, setUserType] = useState<'individual' | 'corporate' | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'borrower' | 'lender'>('borrower');
  
  // User profile state with all necessary fields
  const [formData, setFormData] = useState({
    // Individual fields
    fullName: '',
    phoneNumber: '',
    dateOfBirth: '',
    bvn: '',
    
    // Employment fields (for borrower mode)
    employer: '',
    position: '',
    monthlyIncome: '',
    employmentType: 'full-time' as 'full-time' | 'part-time' | 'self-employed' | 'unemployed',
    
    // Guarantor fields (for borrower mode)
    guarantorName: '',
    guarantorRelationship: '',
    guarantorPhone: '',
    guarantorEmail: '',
    
    // Corporate fields
    organizationName: '',
    officeAddress: '',
    
    // Lender-specific fields
    lenderBusinessName: '',
    lenderBusinessAddress: '',
    lenderYearsInBusiness: '',
    lenderCapitalBase: '',
    lenderLicenseNumber: '',
    lenderBankName: '',
    lenderAccountNumber: '',
    lenderAccountName: '',
    
    // Common fields
    email: ''
  });

  // Save states
  const [savingStates, setSavingStates] = useState({
    profile: false,
    employment: false,
    guarantor: false,
    lender: false
  });

  // Document upload states
  const [uploadingDoc, setUploadingDoc] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Database['public']['Tables']['documents']['Row'][]>([]);

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const user = await getCurrentUser();
        
        if (!user) {
          setError('User not authenticated');
          return;
        }

        setUserId(user.id);

        // Get user profile
        const profile = await getUserProfile(user.id);
        if (profile) {
          setUserType(profile.user_type);
          
          // Set form data based on user type
          setFormData({
            fullName: profile.full_name || '',
            phoneNumber: profile.phone_number || '',
            dateOfBirth: profile.date_of_birth || '',
            bvn: profile.bvn || '',
            employer: profile.employer || '',
            position: profile.position || '',
            monthlyIncome: profile.monthly_income || '',
            employmentType: profile.employment_type || 'full-time',
            guarantorName: profile.guarantor_name || '',
            guarantorRelationship: profile.guarantor_relationship || '',
            guarantorPhone: profile.guarantor_phone || '',
            guarantorEmail: profile.guarantor_email || '',
            organizationName: profile.organization_name || '',
            officeAddress: profile.office_address || '',
            
            // Initialize lender fields (these would come from a lender profile table in production)
            lenderBusinessName: '',
            lenderBusinessAddress: '',
            lenderYearsInBusiness: '',
            lenderCapitalBase: '',
            lenderLicenseNumber: '',
            lenderBankName: '',
            lenderAccountNumber: '',
            lenderAccountName: '',
            
            email: profile.email || user.email || ''
          });
        }

        // Get user documents
        const docsResult = await getUserDocuments(user.id);
        if (docsResult.success && docsResult.documents) {
          setDocuments(docsResult.documents);
        }

      } catch (err) {
        console.error('Error loading user data:', err);
        setError('An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Save profile handler
  const handleSaveProfile = async () => {
    if (!userId) return;
    
    setSavingStates(prev => ({ ...prev, profile: true }));
    try {
      const updates: Record<string, unknown> = {};
      
      if (userType === 'individual') {
        updates.full_name = formData.fullName;
        updates.phone_number = formData.phoneNumber;
        updates.date_of_birth = formData.dateOfBirth;
        updates.bvn = formData.bvn;
      } else if (userType === 'corporate') {
        updates.organization_name = formData.organizationName;
        updates.office_address = formData.officeAddress;
      }
      
      const result = await updateUserProfile(userId, updates);
      if (!result.success) {
        setError(result.error || 'Failed to save profile');
      }
    } catch (err) {
      console.error('Error saving profile:', err);
      setError('An unexpected error occurred');
    } finally {
      setSavingStates(prev => ({ ...prev, profile: false }));
    }
  };

  // Save employment handler
  const handleSaveEmployment = async () => {
    if (!userId || userType !== 'individual') return;
    
    // Validate monthly income is not negative
    if (formData.monthlyIncome && parseFloat(formData.monthlyIncome) < 0) {
      setError('Monthly income cannot be negative');
      return;
    }
    
    setSavingStates(prev => ({ ...prev, employment: true }));
    try {
      const updates = {
        employer: formData.employer,
        position: formData.position,
        monthly_income: formData.monthlyIncome,
        employment_type: formData.employmentType
      };
      
      const result = await updateUserProfile(userId, updates);
      if (!result.success) {
        setError(result.error || 'Failed to save employment information');
      } else {
        // Clear any previous errors on successful save
        setError('');
      }
    } catch (err) {
      console.error('Error saving employment:', err);
      setError('An unexpected error occurred');
    } finally {
      setSavingStates(prev => ({ ...prev, employment: false }));
    }
  };

  // Save guarantor handler
  const handleSaveGuarantor = async () => {
    if (!userId || userType !== 'individual') return;
    
    setSavingStates(prev => ({ ...prev, guarantor: true }));
    try {
      const updates = {
        guarantor_name: formData.guarantorName,
        guarantor_relationship: formData.guarantorRelationship,
        guarantor_phone: formData.guarantorPhone,
        guarantor_email: formData.guarantorEmail
      };
      
      const result = await updateUserProfile(userId, updates);
      if (!result.success) {
        setError(result.error || 'Failed to save guarantor information');
      }
    } catch (err) {
      console.error('Error saving guarantor:', err);
      setError('An unexpected error occurred');
    } finally {
      setSavingStates(prev => ({ ...prev, guarantor: false }));
    }
  };

  // Save lender information handler
  const handleSaveLender = async () => {
    if (!userId) return;
    
    // Validate lender fields before saving
    if (formData.lenderYearsInBusiness && parseFloat(formData.lenderYearsInBusiness) < 0) {
      setError('Years in business cannot be negative');
      return;
    }
    
    if (formData.lenderCapitalBase && parseFloat(formData.lenderCapitalBase) < 0) {
      setError('Capital base cannot be negative');
      return;
    }
    
    // Validate years in business is a whole number
    if (formData.lenderYearsInBusiness && !Number.isInteger(parseFloat(formData.lenderYearsInBusiness))) {
      setError('Years in business must be a whole number');
      return;
    }
    
    setSavingStates(prev => ({ ...prev, lender: true }));
    try {
      // In a real application, you would save lender-specific information to a separate table
      // For now, we'll just log the lender information
      console.log('Lender information saved:', {
        lenderBusinessName: formData.lenderBusinessName,
        lenderBusinessAddress: formData.lenderBusinessAddress,
        lenderYearsInBusiness: formData.lenderYearsInBusiness,
        lenderCapitalBase: formData.lenderCapitalBase,
        lenderLicenseNumber: formData.lenderLicenseNumber,
        lenderBankName: formData.lenderBankName,
        lenderAccountNumber: formData.lenderAccountNumber,
        lenderAccountName: formData.lenderAccountName
      });
      
      // Clear any previous errors on successful save
      setError('');
      
      // TODO: Implement actual lender profile saving
      // const result = await updateLenderProfile(userId, lenderUpdates);
      
    } catch (err) {
      console.error('Error saving lender information:', err);
      setError('An unexpected error occurred');
    } finally {
      setSavingStates(prev => ({ ...prev, lender: false }));
    }
  };

  // Document upload handler
  const handleDocumentUpload = async (documentType: string) => {
    if (!userId) return;
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.jpg,.jpeg,.png,.doc,.docx';
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      setUploadingDoc(documentType);
      try {
        const result = await uploadDocument(userId, file, documentType);
        if (result.success) {
          // Refresh documents
          const docsResult = await getUserDocuments(userId);
          if (docsResult.success && docsResult.documents) {
            setDocuments(docsResult.documents);
          }
        } else {
          setError(result.error || 'Failed to upload document');
        }
      } catch (err) {
        console.error('Error uploading document:', err);
        setError('An unexpected error occurred');
      } finally {
        setUploadingDoc(null);
      }
    };
    
    input.click();
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'uploaded': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: DocumentStatus) => {
    switch (status) {
      case 'verified': return <CheckCircle className="w-4 h-4" />;
      case 'uploaded': return <FileText className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'rejected': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getBorrowerDocuments = () => {
    if (userType === 'individual') {
      return [
        { id: 'bvn', name: 'BVN Verification', type: 'bvn', required: true },
        { id: 'id_card', name: 'Government ID', type: 'id_card', required: true },
        { id: 'utility_bill', name: 'Utility Bill', type: 'utility_bill', required: true },
        { id: 'bank_statement', name: 'Bank Statement', type: 'bank_statement', required: false },
        { id: 'payslip', name: 'Payslip (Last 3 months)', type: 'payslip', required: false },
        { id: 'employment_letter', name: 'Employment Letter', type: 'employment_letter', required: false }
      ];
    } else {
      return [
        { id: 'cac', name: 'CAC Certificate', type: 'cac', required: true },
        { id: 'mermet', name: 'MEMART Certificate', type: 'mermet', required: true },
        { id: 'utility_bill', name: 'Utility Bill', type: 'utility_bill', required: true },
        { id: 'bank_statement', name: 'Bank Statement', type: 'bank_statement', required: true },
        { id: 'financial_statement', name: 'Financial Statement', type: 'financial_statement', required: true }
      ];
    }
  };

  const getLenderDocuments = () => {
    if (userType === 'individual') {
      return [
        { id: 'lenders_license', name: 'Lender\'s License', type: 'lenders_license', required: true },
        { id: 'id_card', name: 'Government ID', type: 'id_card', required: true },
        { id: 'bank_reference', name: 'Bank Reference Letter', type: 'bank_reference', required: true },
        { id: 'tax_clearance', name: 'Tax Clearance Certificate', type: 'tax_clearance', required: true },
        { id: 'proof_of_funds', name: 'Proof of Funds', type: 'proof_of_funds', required: true },
        { id: 'utility_bill', name: 'Utility Bill', type: 'utility_bill', required: false }
      ];
    } else {
      return [
        { id: 'cac', name: 'CAC Certificate', type: 'cac', required: true },
        { id: 'lenders_license', name: 'Lender\'s License', type: 'lenders_license', required: true },
        { id: 'tax_clearance', name: 'Tax Clearance Certificate', type: 'tax_clearance', required: true },
        { id: 'financial_statement', name: 'Audited Financial Statement', type: 'financial_statement', required: true },
        { id: 'bank_reference', name: 'Bank Reference Letter', type: 'bank_reference', required: true },
        { id: 'board_resolution', name: 'Board Resolution', type: 'board_resolution', required: true },
        { id: 'utility_bill', name: 'Utility Bill', type: 'utility_bill', required: false }
      ];
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Error</h1>
          <p className="mt-2 text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">
            Manage your {userType} account settings and documents for both lending and borrowing
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'borrower' | 'lender')} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="borrower" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Borrower Profile
            </TabsTrigger>
            <TabsTrigger value="lender" className="flex items-center gap-2">
              <Building2 className="w-4 h-4" />
              Lender Profile
            </TabsTrigger>
          </TabsList>

          <TabsContent value="borrower" className="space-y-6">
            {/* Profile Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userType === 'individual' ? (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Full Name
                        </label>
                        <Input
                          value={formData.fullName}
                          onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                          placeholder="Enter your full name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Phone Number
                        </label>
                        <Input
                          value={formData.phoneNumber}
                          onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                          placeholder="Enter your phone number"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Date of Birth
                        </label>
                        <Input
                          type="date"
                          value={formData.dateOfBirth}
                          onChange={(e) => setFormData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          BVN
                        </label>
                        <Input
                          value={formData.bvn}
                          onChange={(e) => setFormData(prev => ({ ...prev, bvn: e.target.value }))}
                          placeholder="Enter your BVN"
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Organization Name
                        </label>
                        <Input
                          value={formData.organizationName}
                          onChange={(e) => setFormData(prev => ({ ...prev, organizationName: e.target.value }))}
                          placeholder="Enter organization name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Office Address
                        </label>
                        <Input
                          value={formData.officeAddress}
                          onChange={(e) => setFormData(prev => ({ ...prev, officeAddress: e.target.value }))}
                          placeholder="Enter office address"
                        />
                      </div>
                    </>
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <Input
                      value={formData.email}
                      disabled
                      className="bg-gray-50"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveProfile}
                    disabled={savingStates.profile}
                    className="flex items-center gap-2"
                  >
                    {savingStates.profile ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    Save Changes
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Employment Information - Individual Only */}
            {userType === 'individual' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Employment Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Employer
                      </label>
                      <Input
                        value={formData.employer}
                        onChange={(e) => setFormData(prev => ({ ...prev, employer: e.target.value }))}
                        placeholder="Enter your employer name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Position
                      </label>
                      <Input
                        value={formData.position}
                        onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                        placeholder="Enter your position"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Monthly Income
                      </label>
                      <Input
                        type="number"
                        min="0"
                        step="1000"
                        value={formData.monthlyIncome}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (parseFloat(value) < 0) {
                            setError('Monthly income cannot be negative');
                            return;
                          }
                          setFormData(prev => ({ ...prev, monthlyIncome: value }));
                        }}
                        placeholder="Enter monthly income"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Employment Type
                      </label>
                      <Select
                        value={formData.employmentType}
                        onValueChange={(value: 'full-time' | 'part-time' | 'self-employed' | 'unemployed') => 
                          setFormData(prev => ({ ...prev, employmentType: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select employment type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full-time">Full-time</SelectItem>
                          <SelectItem value="part-time">Part-time</SelectItem>
                          <SelectItem value="self-employed">Self-employed</SelectItem>
                          <SelectItem value="unemployed">Unemployed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      onClick={handleSaveEmployment}
                      disabled={savingStates.employment}
                      className="flex items-center gap-2"
                    >
                      {savingStates.employment ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Save className="w-4 h-4" />
                      )}
                      Save Employment Info
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Guarantor Information - Individual Only */}
            {userType === 'individual' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Guarantor Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Guarantor Name
                      </label>
                      <Input
                        value={formData.guarantorName}
                        onChange={(e) => setFormData(prev => ({ ...prev, guarantorName: e.target.value }))}
                        placeholder="Enter guarantor's full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Relationship
                      </label>
                      <Input
                        value={formData.guarantorRelationship}
                        onChange={(e) => setFormData(prev => ({ ...prev, guarantorRelationship: e.target.value }))}
                        placeholder="e.g., Spouse, Parent, Sibling"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Guarantor Phone
                      </label>
                      <Input
                        value={formData.guarantorPhone}
                        onChange={(e) => setFormData(prev => ({ ...prev, guarantorPhone: e.target.value }))}
                        placeholder="Enter guarantor's phone number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Guarantor Email
                      </label>
                      <Input
                        type="email"
                        value={formData.guarantorEmail}
                        onChange={(e) => setFormData(prev => ({ ...prev, guarantorEmail: e.target.value }))}
                        placeholder="Enter guarantor's email"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      onClick={handleSaveGuarantor}
                      disabled={savingStates.guarantor}
                      className="flex items-center gap-2"
                    >
                      {savingStates.guarantor ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Save className="w-4 h-4" />
                      )}
                      Save Guarantor Info
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Borrower Document Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Borrower Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getBorrowerDocuments().map((doc) => {
                    const uploadedDoc = documents.find(d => d.document_type === doc.type);
                    const isUploading = uploadingDoc === doc.type;
                    
                    return (
                      <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex flex-col">
                            <span className="font-medium">{doc.name}</span>
                            {doc.required && (
                              <Badge variant="secondary" className="w-fit text-xs">
                                Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          {uploadedDoc ? (
                            <Badge className={getStatusColor(uploadedDoc.status)}>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(uploadedDoc.status)}
                                {uploadedDoc.status}
                              </div>
                            </Badge>
                          ) : (
                            <Badge variant="outline">Not uploaded</Badge>
                          )}
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDocumentUpload(doc.type)}
                            disabled={isUploading}
                            className="flex items-center gap-2"
                          >
                            {isUploading ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            ) : (
                              <Plus className="w-4 h-4" />
                            )}
                            {uploadedDoc ? 'Replace' : 'Upload'}
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="lender" className="space-y-6">
            {/* Lender Business Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Lender Business Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Name
                    </label>
                    <Input
                      value={formData.lenderBusinessName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderBusinessName: e.target.value }))}
                      placeholder="Enter your business name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Address
                    </label>
                    <Input
                      value={formData.lenderBusinessAddress}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderBusinessAddress: e.target.value }))}
                      placeholder="Enter your business address"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Years in Business
                    </label>
                    <Input
                      type="number"
                      min="0"
                      step="1"
                      value={formData.lenderYearsInBusiness}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (parseFloat(value) < 0) {
                          setError('Years in business cannot be negative');
                          return;
                        }
                        setFormData(prev => ({ ...prev, lenderYearsInBusiness: value }));
                      }}
                      placeholder="Number of years"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Capital Base (₦)
                    </label>
                    <Input
                      type="number"
                      min="0"
                      step="1000"
                      value={formData.lenderCapitalBase}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (parseFloat(value) < 0) {
                          setError('Capital base cannot be negative');
                          return;
                        }
                        setFormData(prev => ({ ...prev, lenderCapitalBase: value }));
                      }}
                      placeholder="Enter capital base"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      License Number
                    </label>
                    <Input
                      value={formData.lenderLicenseNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderLicenseNumber: e.target.value }))}
                      placeholder="Enter license number"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveLender}
                    disabled={savingStates.lender}
                    className="flex items-center gap-2"
                  >
                    {savingStates.lender ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    Save Lender Info
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Banking Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Banking Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Name
                    </label>
                    <Input
                      value={formData.lenderBankName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderBankName: e.target.value }))}
                      placeholder="Enter bank name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Number
                    </label>
                    <Input
                      value={formData.lenderAccountNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderAccountNumber: e.target.value }))}
                      placeholder="Enter account number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Name
                    </label>
                    <Input
                      value={formData.lenderAccountName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lenderAccountName: e.target.value }))}
                      placeholder="Enter account name"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveLender}
                    disabled={savingStates.lender}
                    className="flex items-center gap-2"
                  >
                    {savingStates.lender ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    Save Changes
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Lender Document Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Lender Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getLenderDocuments().map((doc) => {
                    const uploadedDoc = documents.find(d => d.document_type === doc.type);
                    const isUploading = uploadingDoc === doc.type;
                    
                    return (
                      <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex flex-col">
                            <span className="font-medium">{doc.name}</span>
                            {doc.required && (
                              <Badge variant="secondary" className="w-fit text-xs">
                                Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          {uploadedDoc ? (
                            <Badge className={getStatusColor(uploadedDoc.status)}>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(uploadedDoc.status)}
                                {uploadedDoc.status}
                              </div>
                            </Badge>
                          ) : (
                            <Badge variant="outline">Not uploaded</Badge>
                          )}
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDocumentUpload(doc.type)}
                            disabled={isUploading}
                            className="flex items-center gap-2"
                          >
                            {isUploading ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            ) : (
                              <Plus className="w-4 h-4" />
                            )}
                            {uploadedDoc ? 'Replace' : 'Upload'}
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 
