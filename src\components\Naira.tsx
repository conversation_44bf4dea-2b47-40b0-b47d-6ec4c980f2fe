import React from "react";

type NairaIconProps = {
  className?: string;
};

const NairaIcon: React.FC<NairaIconProps> = ({ className = "w-5 h-5 text-current" }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    {/* Two vertical bars */}
    <path d="M6 4v16M18 4v16" />
    {/* Two horizontal crossbars */}
    <path d="M6 10h12" />
    <path d="M6 14h12" />
    {/* Diagonal lines to form the 'N' */}
    <path d="M6 4l12 16" />
  </svg>
);

export default NairaIcon;
