/**
 * Test file for validation functions
 * Run with: npm test validation.test.ts
 */

import { 
  validateEmail, 
  validatePhoneNumber, 
  validateName, 
  validateRelationship 
} from '../validation';

describe('Email Validation', () => {
  test('should validate correct email addresses', () => {
    expect(validateEmail('<EMAIL>').isValid).toBe(true);
    expect(validateEmail('<EMAIL>').isValid).toBe(true);
    expect(validateEmail('<EMAIL>').isValid).toBe(true);
  });

  test('should reject invalid email addresses', () => {
    expect(validateEmail('invalidemail').isValid).toBe(false);
    expect(validateEmail('test@').isValid).toBe(false);
    expect(validateEmail('@domain.com').isValid).toBe(false);
    expect(validateEmail('<EMAIL>').isValid).toBe(false);
    expect(validateEmail('.<EMAIL>').isValid).toBe(false);
    expect(validateEmail('test@domain.c').isValid).toBe(false);
  });

  test('should reject empty email', () => {
    expect(validateEmail('').isValid).toBe(false);
    expect(validateEmail('   ').isValid).toBe(false);
  });
});

describe('Phone Number Validation', () => {
  test('should validate correct Nigerian phone numbers', () => {
    // 11-digit format starting with 0
    expect(validatePhoneNumber('08012345678').isValid).toBe(true);
    expect(validatePhoneNumber('07012345678').isValid).toBe(true);
    expect(validatePhoneNumber('09012345678').isValid).toBe(true);
    
    // 10-digit format
    expect(validatePhoneNumber('8012345678').isValid).toBe(true);
    expect(validatePhoneNumber('7012345678').isValid).toBe(true);
    expect(validatePhoneNumber('9012345678').isValid).toBe(true);
    
    // 13-digit format with country code
    expect(validatePhoneNumber('2348012345678').isValid).toBe(true);
    expect(validatePhoneNumber('2347012345678').isValid).toBe(true);
    expect(validatePhoneNumber('2349012345678').isValid).toBe(true);
    
    // With formatting characters (should be cleaned)
    expect(validatePhoneNumber('+234 ************').isValid).toBe(true);
    expect(validatePhoneNumber('0801-234-5678').isValid).toBe(true);
  });

  test('should reject invalid phone numbers', () => {
    // Invalid formats
    expect(validatePhoneNumber('abc123').isValid).toBe(false);
    expect(validatePhoneNumber('12345').isValid).toBe(false);
    expect(validatePhoneNumber('01012345678').isValid).toBe(false); // Invalid second digit
    expect(validatePhoneNumber('1012345678').isValid).toBe(false); // Invalid first digit for 10-digit
    expect(validatePhoneNumber('2351012345678').isValid).toBe(false); // Invalid country code
    expect(validatePhoneNumber('23401012345678').isValid).toBe(false); // Invalid fourth digit for 13-digit
  });

  test('should reject empty phone number', () => {
    expect(validatePhoneNumber('').isValid).toBe(false);
    expect(validatePhoneNumber('   ').isValid).toBe(false);
    expect(validatePhoneNumber('---').isValid).toBe(false);
  });
});

describe('Name Validation', () => {
  test('should validate correct names', () => {
    expect(validateName('John Doe').isValid).toBe(true);
    expect(validateName('Mary-Jane Smith').isValid).toBe(true);
    expect(validateName("O'Connor").isValid).toBe(true);
    expect(validateName('Jean-Pierre').isValid).toBe(true);
  });

  test('should reject invalid names', () => {
    expect(validateName('J').isValid).toBe(false); // Too short
    expect(validateName('John123').isValid).toBe(false); // Contains numbers
    expect(validateName('John  Doe').isValid).toBe(false); // Consecutive spaces
    expect(validateName('John@Doe').isValid).toBe(false); // Invalid characters
  });

  test('should reject empty name', () => {
    expect(validateName('').isValid).toBe(false);
    expect(validateName('   ').isValid).toBe(false);
  });
});

describe('Relationship Validation', () => {
  test('should validate correct relationships', () => {
    expect(validateRelationship('Spouse').isValid).toBe(true);
    expect(validateRelationship('Parent').isValid).toBe(true);
    expect(validateRelationship('Business Partner').isValid).toBe(true);
    expect(validateRelationship('Sister-in-law').isValid).toBe(true);
  });

  test('should reject invalid relationships', () => {
    expect(validateRelationship('X').isValid).toBe(false); // Too short
    expect(validateRelationship('Friend123').isValid).toBe(false); // Contains numbers
    expect(validateRelationship('Friend@work').isValid).toBe(false); // Invalid characters
  });

  test('should reject empty relationship', () => {
    expect(validateRelationship('').isValid).toBe(false);
    expect(validateRelationship('   ').isValid).toBe(false);
  });
});

// Test cases that match the bug report scenarios
describe('Bug Report Test Cases', () => {
  test('should reject "abc123" as phone number', () => {
    const result = validatePhoneNumber('abc123');
    expect(result.isValid).toBe(false);
    expect(result.error).toBeDefined();
  });

  test('should reject "invalidemail" as email', () => {
    const result = validateEmail('invalidemail');
    expect(result.isValid).toBe(false);
    expect(result.error).toBeDefined();
  });

  test('should provide helpful error messages', () => {
    expect(validatePhoneNumber('abc123').error).toContain('digits');
    expect(validateEmail('invalidemail').error).toContain('valid email');
    expect(validateName('').error).toContain('required');
    expect(validateRelationship('').error).toContain('required');
  });
});
