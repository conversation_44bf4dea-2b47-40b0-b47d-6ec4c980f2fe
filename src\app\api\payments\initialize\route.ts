import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { amount, email, loanApplicationId, scheduledPaymentMonth, paymentType } = body;

    // Validate required fields
    if (!amount || !email || !loanApplicationId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const NEXT_PUBLIC_PAYSTACK_SECRET_KEY = process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY;
    
    // Enhanced debugging for Amplify deployment
    console.log('Environment check:', {
      hasPaystackKey: !!NEXT_PUBLIC_PAYSTACK_SECRET_KEY,
      keyPrefix: NEXT_PUBLIC_PAYSTACK_SECRET_KEY?.substring(0, 7),
      nodeEnv: process.env.NODE_ENV,
      deployment: process.env.AWS_REGION ? 'amplify' : 'local'
    });
    
    if (!NEXT_PUBLIC_PAYSTACK_SECRET_KEY || NEXT_PUBLIC_PAYSTACK_SECRET_KEY === 'sk_test_your_NEXT_PUBLIC_PAYSTACK_SECRET_KEY') {
      console.error('Paystack secret key not configured properly. Available env vars:', Object.keys(process.env).filter(key => key.includes('PAYSTACK')));
      return NextResponse.json(
        { 
          success: false, 
          message: 'Payment system not configured properly. Please check environment variables.',
          debug: process.env.NODE_ENV === 'development' ? {
            hasKey: !!NEXT_PUBLIC_PAYSTACK_SECRET_KEY,
            availableKeys: Object.keys(process.env).filter(key => key.includes('PAYSTACK'))
          } : undefined
        },
        { status: 500 }
      );
    }

    const reference = `KX-${Date.now()}-${uuidv4().slice(0, 8)}`;
    
    const response = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${NEXT_PUBLIC_PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        amount: Math.round(amount * 100), // Convert to kobo
        reference,
        currency: 'NGN',
        channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
        callback_url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/payment/success?reference=${reference}`,
        metadata: {
          loan_application_id: loanApplicationId,
          scheduled_payment_month: scheduledPaymentMonth,
          payment_type: paymentType || 'regular'
        }
      })
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.error('Paystack API error:', result);
      return NextResponse.json(
        { success: false, message: result.message || 'Failed to initialize payment' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Error initializing payment:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
} 