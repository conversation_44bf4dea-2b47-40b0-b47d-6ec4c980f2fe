import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Only allow this endpoint in development or for debugging
  if (process.env.NODE_ENV === 'production' && !request.nextUrl.searchParams.get('debug')) {
    return NextResponse.json(
      { error: 'Not available in production' },
      { status: 404 }
    );
  }

  try {
    const config = {
      deployment: {
        environment: process.env.NODE_ENV,
        platform: process.env.AWS_REGION ? 'amplify' : 'local',
        region: process.env.AWS_REGION || 'local',
      },
      paystack: {
        hasSecretKey: !!process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY,
        secretKeyPrefix: process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY?.substring(0, 7) || 'not-set',
        hasPublicKey: !!process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
        publicKeyPrefix: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY?.substring(0, 7) || 'not-set',
      },
      app: {
        hasAppUrl: !!process.env.NEXT_PUBLIC_APP_URL,
        appUrl: process.env.NEXT_PUBLIC_APP_URL || 'not-set',
        hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      availableEnvVars: {
        paystack: Object.keys(process.env).filter(key => key.includes('PAYSTACK')),
        supabase: Object.keys(process.env).filter(key => key.includes('SUPABASE')),
        nextPublic: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_')),
      }
    };

    return NextResponse.json(config);

  } catch (error) {
    console.error('Error checking config:', error);
    return NextResponse.json(
      { error: 'Failed to check configuration' },
      { status: 500 }
    );
  }
} 