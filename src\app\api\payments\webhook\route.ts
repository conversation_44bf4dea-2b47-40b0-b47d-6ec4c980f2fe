import { NextRequest, NextResponse } from 'next/server';
import { createHmac } from 'crypto';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-paystack-signature');
    
    if (!signature) {
      console.error('No signature provided');
      return NextResponse.json({ error: 'No signature provided' }, { status: 400 });
    }

    // Verify webhook signature
    const secret = process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY;
    if (!secret) {
      console.error('Paystack secret key not configured');
      return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
    }

    const hash = createHmac('sha512', secret).update(body).digest('hex');
    
    if (hash !== signature) {
      console.error('Invalid signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Parse the webhook data
    const event = JSON.parse(body);
    
    console.log('Webhook event received:', event.event);
    console.log('Event data:', event.data);

    // Handle different event types
    switch (event.event) {
      case 'charge.success':
        await handlePaymentSuccess(event.data);
        break;
      
      case 'charge.failed':
        await handlePaymentFailed(event.data);
        break;
      
      case 'charge.disputed':
        await handlePaymentDisputed(event.data);
        break;
      
      default:
        console.log('Unhandled event type:', event.event);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handlePaymentSuccess(data: {
  reference: string;
  amount: number;
  authorization?: {
    authorization_code?: string;
    card_type?: string;
    bank?: string;
    last4?: string;
  };
  gateway_response?: string;
  paid_at?: string;
}) {
  try {
    const { reference, amount, authorization, gateway_response, paid_at } = data;
    
    console.log('Processing successful payment:', reference);
    
    // Find the payment record by reference
    const { data: paymentRecord, error: paymentError } = await supabase
      .from('loan_payments')
      .select('*')
      .eq('payment_reference', reference)
      .single();

    if (paymentError || !paymentRecord) {
      console.error('Payment record not found:', reference);
      return;
    }

    // Update payment status to completed
    const { error: updateError } = await supabase
      .from('loan_payments')
      .update({
        status: 'completed',
        payment_date: paid_at || new Date().toISOString(),
        gateway_response: gateway_response
      })
      .eq('id', paymentRecord.id);

    if (updateError) {
      console.error('Failed to update payment status:', updateError);
      return;
    }

    // Update transaction record
    const { error: transactionError } = await supabase
      .from('payment_transactions')
      .update({
        status: 'success',
        gateway_response: gateway_response,
        authorization_code: authorization?.authorization_code,
        card_type: authorization?.card_type,
        bank: authorization?.bank,
        last4: authorization?.last4,
        paid_at: paid_at || new Date().toISOString()
      })
      .eq('paystack_reference', reference);

    if (transactionError) {
      console.error('Failed to update transaction:', transactionError);
    }

    // Update payment schedule if this was a scheduled payment
    if (paymentRecord.scheduled_payment_month) {
      const { error: scheduleError } = await supabase
        .from('payment_schedules')
        .update({
          status: 'paid',
          actual_payment_amount: amount / 100, // Convert from kobo to naira
          actual_principal: paymentRecord.principal_amount,
          actual_interest: paymentRecord.interest_amount,
          actual_processing_fee: paymentRecord.processing_fee,
          paid_date: paid_at || new Date().toISOString()
        })
        .eq('loan_application_id', paymentRecord.loan_application_id)
        .eq('month_number', paymentRecord.scheduled_payment_month);

      if (scheduleError) {
        console.error('Failed to update payment schedule:', scheduleError);
      }
    }

    console.log('Payment processed successfully:', reference);
  } catch (error) {
    console.error('Error processing payment success:', error);
  }
}

async function handlePaymentFailed(data: {
  reference: string;
  gateway_response?: string;
}) {
  try {
    const { reference, gateway_response } = data;
    
    console.log('Processing failed payment:', reference);
    
    // Find the payment record by reference
    const { data: paymentRecord, error: paymentError } = await supabase
      .from('loan_payments')
      .select('*')
      .eq('payment_reference', reference)
      .single();

    if (paymentError || !paymentRecord) {
      console.error('Payment record not found:', reference);
      return;
    }

    // Update payment status to failed
    const { error: updateError } = await supabase
      .from('loan_payments')
      .update({
        status: 'failed',
        gateway_response: gateway_response || 'Payment failed'
      })
      .eq('id', paymentRecord.id);

    if (updateError) {
      console.error('Failed to update payment status:', updateError);
      return;
    }

    // Update transaction record
    const { error: transactionError } = await supabase
      .from('payment_transactions')
      .update({
        status: 'failed',
        gateway_response: gateway_response || 'Payment failed'
      })
      .eq('paystack_reference', reference);

    if (transactionError) {
      console.error('Failed to update transaction:', transactionError);
    }

    console.log('Failed payment processed:', reference);
  } catch (error) {
    console.error('Error processing payment failure:', error);
  }
}

async function handlePaymentDisputed(data: {
  reference: string;
  gateway_response?: string;
}) {
  try {
    const { reference, gateway_response } = data;
    
    console.log('Processing disputed payment:', reference);
    
    // Find the payment record by reference
    const { data: paymentRecord, error: paymentError } = await supabase
      .from('loan_payments')
      .select('*')
      .eq('payment_reference', reference)
      .single();

    if (paymentError || !paymentRecord) {
      console.error('Payment record not found:', reference);
      return;
    }

    // Update payment status to disputed (you might want to add this status to your enum)
    const { error: updateError } = await supabase
      .from('loan_payments')
      .update({
        status: 'failed', // or create a 'disputed' status
        gateway_response: gateway_response || 'Payment disputed'
      })
      .eq('id', paymentRecord.id);

    if (updateError) {
      console.error('Failed to update payment status:', updateError);
      return;
    }

    // Update transaction record
    const { error: transactionError } = await supabase
      .from('payment_transactions')
      .update({
        status: 'failed',
        gateway_response: gateway_response || 'Payment disputed'
      })
      .eq('paystack_reference', reference);

    if (transactionError) {
      console.error('Failed to update transaction:', transactionError);
    }

    console.log('Disputed payment processed:', reference);
  } catch (error) {
    console.error('Error processing payment dispute:', error);
  }
} 