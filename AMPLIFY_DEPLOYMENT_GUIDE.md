# AWS Amplify Deployment Guide for Kredxa Payment System

## Issue Diagnosis

The payment initialization is failing on Amplify because environment variables are not properly configured. This guide will help you fix the issue.

## 1. Check Current Configuration

First, visit this diagnostic endpoint on your deployed app:
```
https://your-amplify-domain.com/api/payments/config?debug=true
```

This will show you what environment variables are available and properly configured.

## 2. Required Environment Variables for Amplify

### In AWS Amplify Console → Your App → Environment Variables:

#### **Essential Payment Variables:**
```bash
NEXT_PUBLIC_PAYSTACK_SECRET_KEY=sk_test_your_actual_secret_key_here
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_actual_public_key_here
NEXT_PUBLIC_APP_URL=https://your-amplify-domain.com
```

#### **Database Variables:**
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### **Optional Variables:**
```bash
NODE_ENV=production
```

## 3. Amplify-Specific Configuration Steps

### Step 1: Navigate to Environment Variables
1. Go to AWS Amplify Console
2. Select your app
3. Go to "App settings" → "Environment variables"
4. Add each variable listed above

### Step 2: Important Notes for Amplify
- **DO NOT** include quotes around the values
- **DO NOT** include export statements
- Variables should be added exactly as shown above
- `NEXT_PUBLIC_` variables are accessible to client-side code
- Variables without `NEXT_PUBLIC_` are server-side only

### Step 3: Redeploy After Adding Variables
1. Go to "App settings" → "Build settings"
2. Click "Redeploy this version" or push a new commit
3. Wait for build to complete

## 4. Troubleshooting Common Issues

### Issue 1: "Payment system not configured properly"
**Cause:** `NEXT_PUBLIC_PAYSTACK_SECRET_KEY` is missing or incorrect
**Solution:** 
- Verify the secret key starts with `sk_test_` or `sk_live_`
- Ensure no extra spaces or characters
- Check the diagnostic endpoint: `/api/payments/config?debug=true`

### Issue 2: Environment variables not showing up
**Cause:** Next.js caching or build issues
**Solution:**
1. Clear Amplify cache: Go to App settings → Build settings → Clear cache
2. Redeploy the app
3. Check diagnostic endpoint again

### Issue 3: Variables work locally but not on Amplify
**Cause:** Different environment variable handling
**Solution:**
- Ensure all server-side variables are added to Amplify console (not just .env file)
- Client-side variables must have `NEXT_PUBLIC_` prefix
- Restart the build after adding variables

## 5. Testing Your Configuration

### Test 1: Check Diagnostic Endpoint
```bash
curl https://your-app.amplifyapp.com/api/payments/config?debug=true
```

Should return:
```json
{
  "paystack": {
    "hasSecretKey": true,
    "secretKeyPrefix": "sk_test",
    "hasPublicKey": true,
    "publicKeyPrefix": "pk_test"
  },
  "deployment": {
    "platform": "amplify"
  }
}
```

### Test 2: Test Payment Initialization
Try making a payment through the app and check browser console and network tab for detailed error messages.

## 6. Security Best Practices

### For Production Deployment:
1. Use live Paystack keys (`sk_live_` and `pk_live_`)
2. Set up proper webhook endpoints
3. Remove or secure the diagnostic endpoint
4. Enable proper CORS policies

### Environment Separation:
```bash
# For staging/development branch
NEXT_PUBLIC_PAYSTACK_SECRET_KEY=sk_test_...
PAYSTACK_PUBLIC_KEY=pk_test_...

# For production branch  
NEXT_PUBLIC_PAYSTACK_SECRET_KEY=sk_live_...
PAYSTACK_PUBLIC_KEY=pk_live_...
```

## 7. Common Amplify Gotchas

1. **Case Sensitivity:** Environment variable names are case-sensitive
2. **Rebuild Required:** Always redeploy after changing environment variables
3. **Branch-Specific:** Environment variables can be set per branch
4. **No Quotes:** Don't wrap values in quotes in Amplify console
5. **Client vs Server:** Only `NEXT_PUBLIC_` variables are available in browser

## 8. Quick Fix Commands

If you have AWS CLI configured:

```bash
# Check current environment variables
aws amplify get-app --app-id YOUR_APP_ID

# Add environment variable via CLI
aws amplify update-app --app-id YOUR_APP_ID --environment-variables NEXT_PUBLIC_PAYSTACK_SECRET_KEY=sk_test_your_key
```

## 9. Support

If issues persist:
1. Check Amplify build logs for specific errors
2. Visit the diagnostic endpoint for current configuration status  
3. Verify Paystack dashboard settings match your keys
4. Ensure webhook URLs point to your Amplify domain

---

**Next Steps After Fixing:**
1. Verify payment initialization works
2. Test complete payment flow
3. Check webhook delivery in Paystack dashboard
4. Monitor application logs for any remaining issues 